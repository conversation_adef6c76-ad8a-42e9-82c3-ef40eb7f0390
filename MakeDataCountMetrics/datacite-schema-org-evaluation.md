# DataCite vs Schema.org Metadata Structure Evaluation for DRH

## Executive Summary

This document evaluates DataCite Metadata Schema 4.6 and Schema.org Dataset type for structuring metadata in the Diabetes Research Hub (DRH) project. Both standards offer robust frameworks for research data description, but serve different primary purposes and use cases.

**Recommendation**: Use DataCite as the primary metadata standard for DOI registration and citation, with Schema.org as a complementary standard for web discoverability and SEO.

## Overview of Standards

### DataCite Metadata Schema 4.6
- **Purpose**: Designed specifically for research data citation and DOI registration
- **Governance**: Maintained by DataCite organization with community input
- **Focus**: Academic citation, persistent identification, and scholarly communication
- **Latest Version**: 4.6 (Released December 2024)
- **Format**: XML-based with JSON representation available

### Schema.org Dataset Type
- **Purpose**: General-purpose structured data for web content and search engines
- **Governance**: Maintained by Schema.org community (Google, Microsoft, Yahoo, Yandex)
- **Focus**: Web discoverability, search engine optimization, and linked data
- **Format**: JSON-LD, Microdata, or RDFa
- **Inheritance**: Extends CreativeWork → Thing

## Detailed Comparison

### 1. Core Metadata Properties

#### DataCite Required Fields
```xml
<titles>
<creators>
<publicationYear>
<resourceType>
<publisher>
```

#### Schema.org Core Properties
```json
{
  "name": "Dataset title",
  "creator": "Author/Organization",
  "datePublished": "Publication date",
  "description": "Dataset description",
  "url": "Dataset URL"
}
```

### 2. Research-Specific Features

| Feature | DataCite | Schema.org | DRH Relevance |
|---------|----------|------------|---------------|
| **DOI Support** | Native, required | Via identifier property | Critical for DRH citations |
| **Funding Information** | fundingReferences | funding property | Essential for grant reporting |
| **Research Subjects** | subjects with schemes | keywords, about | Important for diabetes research categorization |
| **Contributors/Roles** | contributors with types | contributor, author | Needed for multi-investigator studies |
| **Related Resources** | relatedIdentifiers | citation, isBasedOn | Useful for study relationships |
| **Geolocation** | geoLocations | spatialCoverage | Relevant for multi-site studies |
| **Temporal Coverage** | dates with types | temporalCoverage | Important for longitudinal studies |
| **File Information** | sizes, formats | distribution, encodingFormat | Critical for CGM data files |

### 3. Diabetes Research Hub Specific Requirements

#### CGM Data Characteristics
```json
// DataCite approach
{
  "subjects": [
    {"subject": "Diabetes", "subjectScheme": "MeSH"},
    {"subject": "Continuous Glucose Monitoring"},
    {"subject": "Type 1 Diabetes", "subjectScheme": "SNOMED CT"}
  ],
  "descriptions": [
    {
      "description": "CGM data from 100 participants over 14 days",
      "descriptionType": "Abstract"
    },
    {
      "description": "Inclusion: T1D diagnosis >1 year, age 18-65",
      "descriptionType": "Methods"
    }
  ]
}

// Schema.org approach
{
  "keywords": ["Diabetes", "CGM", "Type 1 Diabetes"],
  "about": [
    {"@type": "MedicalCondition", "name": "Type 1 Diabetes"},
    {"@type": "MedicalDevice", "name": "Continuous Glucose Monitor"}
  ],
  "measurementTechnique": "Continuous Glucose Monitoring",
  "variableMeasured": [
    {
      "@type": "PropertyValue",
      "name": "glucose_level",
      "description": "Interstitial glucose concentration",
      "unitText": "mg/dL"
    }
  ]
}
```

### 4. Integration Capabilities

#### DataCite Integration
- **Strengths**:
  - Direct DOI minting and registration
  - Automatic citation tracking
  - Integration with academic databases
  - COUNTER-compliant usage statistics
  - Make Data Count native support

- **Implementation in DRH**:
```java
// From existing DRH codebase
public DataCiteMetadata mapStudyToDataCite(StudyData studyData) {
    return DataCiteMetadata.builder()
            .titles(mapTitles(studyData))
            .creators(mapCreators(studyData.getInvestigators()))
            .subjects([
                Subject.builder().subject("Diabetes").build(),
                Subject.builder().subject("Continuous Glucose Monitoring").build()
            ])
            .resourceType(ResourceType.builder()
                    .resourceTypeGeneral("Dataset")
                    .resourceType("CGM Dataset")
                    .build())
            .build();
}
```

#### Schema.org Integration
- **Strengths**:
  - Enhanced web discoverability
  - Search engine optimization
  - Rich snippets in search results
  - Broader ecosystem support
  - Flexible property extensions

- **Implementation Example**:
```json
{
  "@context": "https://schema.org/",
  "@type": "Dataset",
  "name": "DRH Study #123: CGM Data Analysis",
  "description": "14-day CGM data from 100 T1D participants",
  "creator": {
    "@type": "Organization",
    "name": "Diabetes Research Hub"
  },
  "includedInDataCatalog": {
    "@type": "DataCatalog",
    "name": "DRH Data Repository"
  },
  "distribution": {
    "@type": "DataDownload",
    "contentUrl": "https://drh.org/datasets/study-123.csv",
    "encodingFormat": "text/csv"
  }
}
```

## Evaluation Criteria

### 1. Academic Citation Requirements
- **DataCite**: ⭐⭐⭐⭐⭐ (Designed for this purpose)
- **Schema.org**: ⭐⭐⭐ (Supports citation but not primary focus)

### 2. Web Discoverability
- **DataCite**: ⭐⭐⭐ (Good through DOI landing pages)
- **Schema.org**: ⭐⭐⭐⭐⭐ (Optimized for search engines)

### 3. Research Data Specificity
- **DataCite**: ⭐⭐⭐⭐⭐ (Research-focused properties)
- **Schema.org**: ⭐⭐⭐⭐ (General with research extensions)

### 4. Implementation Complexity
- **DataCite**: ⭐⭐⭐ (Requires DOI registration workflow)
- **Schema.org**: ⭐⭐⭐⭐ (Simpler web integration)

### 5. Diabetes Research Alignment
- **DataCite**: ⭐⭐⭐⭐ (Strong subject classification)
- **Schema.org**: ⭐⭐⭐⭐ (Flexible medical ontology support)

## Recommended Hybrid Approach

### Primary: DataCite for Research Data
```xml
<!-- Core research metadata -->
<resource>
  <titles>
    <title>DRH Study #123: Impact of Exercise on CGM Patterns</title>
  </titles>
  <creators>
    <creator>
      <creatorName>Dr. Sarah Johnson</creatorName>
      <nameIdentifier nameIdentifierScheme="ORCID">0000-0002-1825-0097</nameIdentifier>
    </creator>
  </creators>
  <subjects>
    <subject subjectScheme="MeSH">Diabetes Mellitus, Type 1</subject>
    <subject>Continuous Glucose Monitoring</subject>
    <subject>Exercise Physiology</subject>
  </subjects>
  <fundingReferences>
    <fundingReference>
      <funderName>National Institute of Diabetes</funderName>
      <funderIdentifier funderIdentifierType="Crossref Funder ID">100000062</funderIdentifier>
      <awardNumber>DK123456</awardNumber>
    </fundingReference>
  </fundingReferences>
</resource>
```

### Complementary: Schema.org for Web Presence
```json
{
  "@context": "https://schema.org/",
  "@type": "Dataset",
  "identifier": {
    "@type": "PropertyValue",
    "propertyID": "doi",
    "value": "10.5555/drh.study.123"
  },
  "sameAs": "https://doi.org/10.5555/drh.study.123",
  "measurementTechnique": "Continuous Glucose Monitoring",
  "variableMeasured": [
    {
      "@type": "PropertyValue", 
      "name": "glucose_level",
      "unitText": "mg/dL"
    }
  ],
  "healthCondition": {
    "@type": "MedicalCondition",
    "name": "Type 1 Diabetes"
  }
}
```

## Implementation Recommendations

### 1. Metadata Workflow
1. **Create DataCite metadata** for DOI registration and academic citation
2. **Generate Schema.org markup** from DataCite metadata for web pages
3. **Maintain bidirectional mapping** between the two formats
4. **Validate both formats** before publication

### 2. Technical Implementation
```java
// Metadata service interface
public interface MetadataService {
    DataCiteMetadata createDataCiteMetadata(StudyData study);
    SchemaOrgDataset createSchemaOrgMetadata(StudyData study);
    void validateMetadata(Object metadata);
    String generateDOI(DataCiteMetadata metadata);
}
```

### 3. Quality Assurance
- **Required field validation** for both standards
- **Controlled vocabulary compliance** (MeSH, SNOMED CT)
- **ORCID validation** for researchers
- **Funding identifier verification**

## Conclusion

Both DataCite and Schema.org serve important but different roles in the DRH metadata ecosystem:

- **DataCite** provides the authoritative, research-focused metadata for academic citation and DOI registration
- **Schema.org** enhances web discoverability and provides richer semantic markup for search engines

The hybrid approach leverages the strengths of both standards while maintaining consistency and avoiding duplication of effort. This strategy aligns with current best practices in research data management and supports both academic and public discovery of DRH datasets.

---

**Document Version**: 1.0  
**Last Updated**: January 2024  
**Next Review**: June 2024
